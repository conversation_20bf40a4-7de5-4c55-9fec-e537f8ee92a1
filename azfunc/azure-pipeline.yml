pool:
  name: Azure Pipelines
  demands: npm
  vmImage: ubuntu-22.04

parameters:
- name: skipCaching
  displayName: Skip Caching
  type: boolean
  default: false

# CIの実行トリガー
# mainブランチのazfunc階層以下が更新されたときにだけトリガー
trigger:
  branches:
    include:
    - main
  paths:
    include:
    - azfunc
    exclude:
    - azfunc/.vscode
    - azfunc/README.md

variables:
  NPM_CACHE_DIR: $(Pipeline.Workspace)/.npm

steps:
  # node.jsのバージョンを指定
  - task: NodeTool@0
    displayName: 'node v20.18.1'
    inputs:
      versionSpec: '20.18.1'

  # node_modulesのキャッシュ
  # https://docs.microsoft.com/ja-jp/azure/devops/pipelines/release/caching?view=azure-devops#nodejsnpm
  # https://stackoverflow.com/questions/59221117/azure-pipelines-cache2-fails-with-errorthe-system-cannot-find-the-file-s
  - task: Cache@2
    displayName: 'node_modulesのキャッシュ'
    condition: not(${{ parameters.skipCaching }})
    inputs:
      key: 'npm | "$(Agent.OS)" | $(System.DefaultWorkingDirectory)/azfunc/package-lock.json'
      estoreKeys: |
        npm | "$(Agent.OS)"
      path: $(NPM_CACHE_DIR)
      cacheHitVar: CACHE_RESTORED

  # npmインストール
  - task: Npm@1
    displayName: 'npm ci'
    inputs:
      command: custom
      workingDir: 'azfunc'
      verbose: false
      customCommand: 'ci --cache $(NPM_CACHE_DIR)'

  # ビルドを実行
  - task: Npm@1
    displayName: 'npm run build'
    inputs:
      command: custom
      workingDir: 'azfunc'
      verbose: false
      customCommand: 'run build'

  # テストカバレッジを出力
  - task: Npm@1
    displayName: 'npm run test:ci'
    inputs:
      command: custom
      workingDir: azfunc
      verbose: false
      customCommand: 'run test:ci'

  # テスト結果をpublish
  - task: PublishTestResults@2
    displayName: 'テスト結果をpublish'
    inputs:
      testResultsFiles: '**/junit.xml'
    condition: succeededOrFailed()

  # カバレッジ情報をpublish
  - task: PublishCodeCoverageResults@1
    displayName: 'カバレッジ情報をpublish'
    inputs:
      codeCoverageTool: Cobertura
      summaryFileLocation: '$(System.DefaultWorkingDirectory)/azfunc/**/coverage/cobertura-coverage.xml'

  # Azure Functionsのビルド結果からアーカイブに含める範囲をコピー

  - task: CopyFiles@2
    displayName: 'ビルド結果をコピー'
    inputs:
      sourceFolder: './azfunc'
      contents: |
        **/!(*.ts|*.test.*)
        !.vscode/**/*
        !coverage/**/*
        !(.editorconfig|.*ignore|.npmrc|azure-pipeline.yml|local.settings.json|tsconfig.json)
      targetFolder: '$(System.DefaultWorkingDirectory)/drop'


  # Azure Functionsのビルド結果をzip圧縮
  - task: ArchiveFiles@2
    displayName: 'アーカイブ'
    inputs:
      rootFolderOrFile: '$(System.DefaultWorkingDirectory)/drop'
      includeRootFolder: false
      archiveFile: '$(Build.ArtifactStagingDirectory)/build/azfunc.zip'

  # Azure Functionsのビルド結果をアーティファクトとしてpublish
  - task: PublishPipelineArtifact@1
    displayName: 'Azure Functionsのビルド結果をアーティファクト名dropとしてpublish'
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/build'
      artifact: 'drop'
