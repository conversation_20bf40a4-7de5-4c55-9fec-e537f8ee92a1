parameters:
  - name: companyName
    type: string

steps:
  # Azure Functionsのビルド結果からアーカイブに含める範囲をコピー
  - task: CopyFiles@2
    displayName: 'ビルド結果をコピー (${{ parameters.companyName }})'
    inputs:
      sourceFolder: './azfunc'
      contents: |
        **/!(*.ts|*.test.*)
        !.vscode/**/*
        !coverage/**/*
        !replacement-files/**/*
        !(.editorconfig|.*ignore|.npmrc|azure-pipeline.yml|local.settings.json|tsconfig.json)
      targetFolder: '$(System.DefaultWorkingDirectory)/drop/${{ parameters.companyName }}'

  # 特定のファイルを置き換える
  - task: CopyFiles@2
    displayName: 'Replace function.json'
    inputs:
      sourceFolder: './azfunc/replacement-files/${{ parameters.companyName }}'
      contents: 'function.json'
      targetFolder: '$(System.DefaultWorkingDirectory)/drop/${{ parameters.companyName }}/SendUsagePromotion'
      overwrite: true

  # Azure Functionsのビルド結果をzip圧縮
  - task: ArchiveFiles@2
    displayName: 'アーカイブ (${{ parameters.companyName }})'
    inputs:
      rootFolderOrFile: '$(System.DefaultWorkingDirectory)/drop/${{ parameters.companyName }}'
      includeRootFolder: false
      archiveFile: '$(Build.ArtifactStagingDirectory)/build/azfunc_${{ parameters.companyName }}.zip'
