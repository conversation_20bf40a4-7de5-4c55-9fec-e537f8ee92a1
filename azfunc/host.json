{"version": "2.0", "functionTimeout": "00:30:00", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[3.15.0, 4.0.0)"}, "concurrency": {"dynamicConcurrencyEnabled": true, "snapshotPersistenceEnabled": true}, "extensions": {"queues": {"batchSize": 25}}, "healthMonitor": {"enabled": false}}