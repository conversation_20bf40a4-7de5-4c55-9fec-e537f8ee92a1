// import { AzureFunction, Context, HttpRequest } from "@azure/functions";
// import CryptoJS from 'crypto-js';

// const SECRET_KEY = process.env['AES_SECRET_KEY']!;
// const IV = CryptoJS.enc.Hex.parse(process.env['AES_IV_KEY']!);
// const BATCH_SIZE = 20;

//  function decrypt(encryptedMessage: string): string {
//   try {
//     const key = CryptoJS.enc.Utf8.parse(SECRET_KEY);
//     const decrypted = CryptoJS.AES.decrypt(
//       CryptoJS.lib.CipherParams.create({
//         ciphertext: CryptoJS.enc.Base64.parse(encryptedMessage)
//       }),
//       key,
//       {
//         iv: IV,
//         mode: CryptoJS.mode.CBC,
//         padding: CryptoJS.pad.Pkcs7
//       }
//     );
//     return decrypted.toString(CryptoJS.enc.Utf8);
//   } catch (error) {
//     throw new Error('Decryption Failed: ' + (error instanceof Error ? error.message : 'Unknown Error'));
//   }
// }

// async function processBatch(batch: any[], logger: any) {
//   const results = await Promise.all(batch.map(async (value) => {
//     try {
//       const requestData = value.data;
//       const id = value.recordId;

//       if (!requestData) {
//         logger.error('Data missing for record:', { recordId: id, requestId: requestData?.id || null });
//         return {
//           recordId: id,
//           id: requestData?.id || null,
//           success: false,
//           data: null,
//           errors: [{ message: "Data is missing" }]
//         };
//       }

//       const decryptedData = {
//         id: requestData.id ?? null,
//         d_replyToId: requestData.replyToId ? decrypt(requestData.replyToId) : null,
//         d_messageType: requestData.messageType ? decrypt(requestData.messageType) : null,
//         d_chatId: requestData.chatId ?? null,
//         d_from: requestData.from ? {
//           application: requestData.from.application ? {
//             displayName: decrypt(requestData.from.application.displayName)
//           } : null,
//           device: requestData.from.device ? {
//               displayName: decrypt(requestData.from.device.displayName)
//           } : null,
//           user: requestData.from.user ? {
//               displayName: decrypt(requestData.from.user.displayName)
//           } : null
//         } : null,
//         d_body: {
//           content: requestData.body?.content ? decrypt(requestData.body?.content) : null,
//         },
//         d_hasAttachments: requestData.hasAttachments ?? null,
//         d_channelIdentity: requestData.channelIdentity ? {
//           teamId: requestData.channelIdentity.teamId ?? null,
//           channelId: requestData.channelIdentity.channelId ? decrypt(requestData.channelIdentity.channelId) : null
//         } : null
//       };

//       logger.info('Success Decrypting:', {
//         recordId: id,
//         id: requestData.id,
//         data: decryptedData
//       });

//       return {
//         recordId: id,
//         id: requestData.id,
//         success: true,
//         data: decryptedData,
//         errors: null
//       };
//     } catch (error) {
//       const recordId = value.recordId;
//       const dataId = value.data?.id ?? 'unknown';

//       logger.error(`Error processing record:`, {
//         recordId,
//         id: dataId,
//         error: error instanceof Error ? error.message : 'An unexpected error occurred'
//       });

//       return {
//         recordId,
//         id: dataId,
//         success: false,
//         data: null,
//         errors: [{ message: error instanceof Error ? error.message : 'An unexpected error occurred' }]
//       };
//     }
//   }));

//   return results;
// }

// const main: AzureFunction = async function (context: Context, req: HttpRequest): Promise<void> {
//   const logger = context.log;
//   logger('Decrypt function processed start.');
//   try {
//     if (!SECRET_KEY) {
//       context.log.error('SECRET_KEY not configured');
//       context.res = {
//         status: 500,
//         headers: {
//           "Content-Type": "application/json"
//         },
//         body: JSON.stringify({ values: [] })
//       };
//       return;
//     }

//     if (!req.body?.values || !Array.isArray(req.body.values) || req.body.values.length === 0) {
//       context.res = {
//         status: 400,
//         headers: {
//           "Content-Type": "application/json"
//         },
//         body: JSON.stringify({ values: [] })
//       };
//       return;
//     }

//     logger('req:', req);

//     const decryptedValues = [];
//     const batches = [];

//     for (let i = 0; i < req.body.values.length; i += BATCH_SIZE) {
//       batches.push(req.body.values.slice(i, i + BATCH_SIZE));
//     }

//     for (const batch of batches) {
//       const batchResults = await processBatch(batch, logger);
//       decryptedValues.push(...batchResults);
//     }

//     context.res = {
//       status: 200,
//       headers: {
//         "Content-Type": "application/json"
//       },
//       body: JSON.stringify({ values: decryptedValues })
//     };

//   } catch (error) {
//     logger.error('General error in decrypt function:', error);
//     context.res = {
//       status: 500,
//       body: {
//         error: error instanceof Error ? error.message : 'An unexpected error occurred'
//       }
//     };
//   }
// };

// export default main;