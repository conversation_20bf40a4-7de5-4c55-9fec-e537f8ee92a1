import axios from "axios";
import ISearchContext from "../types/ISearchContext";
import { executeBatch, IExecuteBatchParamBatch } from "./api";

jest.mock('axios');

const axiosPost = (axios.post as jest.MockedFunction<typeof axios.post>);

const standardExecuteBatchParam: IExecuteBatchParamBatch = {
  process: 'batch',
  token: 'token',
  endPoint: 'endpoint',
  context: [
    {
      properties: {
        prop1: 'value1-1',
        prop2: 'value1-2',
      },
      page: 1,
    },
    {
      properties: {
        prop1: 'value2-1',
        prop2: 'value2-2',
      },
      page: 1,
    },
  ],
  getIds: (body: any[]) => (body),
  getPagingContext: (context: ISearchContext, body: any[]) => undefined,
  getRequest: (context: ISearchContext) => ({
    id: '',
    url: ''
  }), 
  getUniqueKey: (context: ISearchContext) => context.properties['prop1'],
};

describe('executeBatch', () => {
  beforeEach(() => {
    axiosPost.mockClear();
  });

  it('skips the batch execution if the context is empty', async () => {
    // arrange
    const param = {
      ...standardExecuteBatchParam,
      context: [],
    }
    // act
    const _result = await executeBatch(param);

    // assert
    expect(axiosPost).not.toBeCalled();
  });

  describe('createBatchRequest', () => {
    beforeEach(() => {
      axiosPost.mockClear();
      axiosPost.mockResolvedValue({
        headers: {
          'content-type': 'multipart/mixed; boundary=boundary_000',
        },
        data: '',
      })
    });
    it('adds changesets if the method is not get', async () => {
      // assert
      const param = {
        ...standardExecuteBatchParam,
        getRequest: (context: ISearchContext) => ({
          id: '',
          url: `url_${context.properties.prop1}`,
          method: 'POST',
          body: `body_${context.properties.prop2}`,
        }), 
      }

      // act
      const result = executeBatch(param);

      // assert
      await expect(result).resolves.toBeTruthy();
      const body = axiosPost.mock.calls[0][1];
      const contentType = axiosPost.mock.calls[0][2]?.headers?.["content-type"];
      expect(contentType).toMatch(/^multipart\/mixed; boundary=/);
      const boundary = contentType.replace(/^multipart\/mixed; boundary=/, '');

      const pattern = new RegExp([
        `--${boundary}`,
        `content-type: multipart/mixed; boundary=([^\r]*)`,
        '',
        '--\\1',
        'content-type: application/http',
        'content-transfer-encoding: binary',
        'content-id: 0',
        '',
        'POST url_value1-1 HTTP/1.1',
        '',
        'body_value1-2',
        '',
        '--\\1--',
        `--${boundary}`,
        `content-type: multipart/mixed; boundary=([^\r]*)`,
        '',
        '--\\2',
        'content-type: application/http',
        'content-transfer-encoding: binary',
        'content-id: 1',
        '',
        'POST url_value2-1 HTTP/1.1',
        '',
        'body_value2-2',
        '',
        '--\\2--',
        `--${boundary}--`,
        '',
      ].join('\r\n'));
      expect(body).toMatch(pattern); 
    });

    it('does not add changesets if the method is get', async () => {
      // assert
      const param = {
        ...standardExecuteBatchParam,
        getRequest: (context: ISearchContext) => ({
          id: '',
          url: `url_${context.properties.prop1}`,
          method: 'GET',
        }), 
      }

      // act
      const result = executeBatch(param);

      // assert
      await expect(result).resolves.toBeTruthy();
      const body = axiosPost.mock.calls[0][1];
      const contentType = axiosPost.mock.calls[0][2]?.headers?.["content-type"];
      expect(contentType).toMatch(/^multipart\/mixed; boundary=/);
      const boundary = contentType.replace(/^multipart\/mixed; boundary=/, '');

      const pattern = [
        `--${boundary}`,
        'content-type: application/http',
        'content-transfer-encoding: binary',
        'content-id: 0',
        '',
        'GET url_value1-1 HTTP/1.1',
        '',
        `--${boundary}`,
        'content-type: application/http',
        'content-transfer-encoding: binary',
        'content-id: 1',
        '',
        'GET url_value2-1 HTTP/1.1',
        '',
        `--${boundary}--`,
        '',
      ].join('\r\n');
      expect(body).toBe(pattern); 
    });

    it('adds the specified headers', async () => {
      // assert
      const param = {
        ...standardExecuteBatchParam,
        getRequest: (context: ISearchContext) => ({
          id: '',
          url: `url_${context.properties.prop1}`,
          method: 'GET',
          headers: {
            header1: 'value1',
            header2: 'value2',
          }
        }), 
      }

      // act
      const result = executeBatch(param);

      // assert
      await expect(result).resolves.toBeTruthy();
      const body = axiosPost.mock.calls[0][1];
      const contentType = axiosPost.mock.calls[0][2]?.headers?.["content-type"];
      expect(contentType).toMatch(/^multipart\/mixed; boundary=/);
      const boundary = contentType.replace(/^multipart\/mixed; boundary=/, '');

      const pattern = [
        `--${boundary}`,
        'content-type: application/http',
        'content-transfer-encoding: binary',
        'content-id: 0',
        '',
        'GET url_value1-1 HTTP/1.1',
        'header1: value1',
        'header2: value2',
        '',
        `--${boundary}`,
        'content-type: application/http',
        'content-transfer-encoding: binary',
        'content-id: 1',
        '',
        'GET url_value2-1 HTTP/1.1',
        'header1: value1',
        'header2: value2',
        '',
        `--${boundary}--`,
        '',
      ].join('\r\n');
      expect(body).toBe(pattern); 
    });

    it('creates the url and the headers', async () => {
      // assert
      const param = {
        ...standardExecuteBatchParam,
        getRequest: (context: ISearchContext) => ({
          id: '',
          url: `url_${context.properties.prop1}`,
          method: 'GET',
          headers: {
            header1: 'value1',
            header2: 'value2',
          }
        }), 
      }

      // act
      const result = executeBatch(param);

      // assert
      await expect(result).resolves.toBeTruthy();
      const url = axiosPost.mock.calls[0][0];
      const authorization = axiosPost.mock.calls[0][2]?.headers?.['authorization'];

      expect(url).toBe('endpoint/$batch');
      expect(authorization).toBe('Bearer token');
    });
  });

  describe('splitResponse', () => {
    it('throws error if the content-type header does not exist', async () => {
      // arrange
      axiosPost.mockResolvedValue({
        headers: {
        },
      });
  
      // act
      const result = executeBatch(standardExecuteBatchParam);

      // assert
      await expect(result).rejects.toThrowError('response cannot be parsed');
    });

    it('throws error if the content-type header does not include boundary', async () => {
      // arrange
      axiosPost.mockResolvedValue({
        headers: {
          'content-type': 'application/json',
        },
      });
  
      // act
      const result = executeBatch(standardExecuteBatchParam);

      // assert
      await expect(result).rejects.toThrowError('response cannot be parsed');
    });

    it('splits the result', async () => {
      // arrange
      const param = {
        ...standardExecuteBatchParam,
        context: [
          {
            properties: {
              prop1: 'value1-1',
              prop2: 'value1-2',  
            },
            page: 1,
          },
          {
            properties: {
              prop1: 'value2-1',
              prop2: 'value2-2',  
            },
            page: 1,
          },
          {
            properties: {
              prop1: 'value3-1',
              prop2: 'value3-2',  
            },
            page: 1,
          },
          {
            properties: {
              prop1: 'value4-1',
              prop2: 'value4-2',  
            },
            page: 1,
          },
          {
            properties: {
              prop1: 'value5-1',
              prop2: 'value5-2',  
            },
            page: 1,
          },
          {
            properties: {
              prop1: 'value6-1',
              prop2: 'value6-2',  
            },
            page: 1,
          },
          {
            properties: {
              prop1: 'value7-1',
              prop2: 'value7-2',  
            },
            page: 1,
          },
        ],
      }

      axiosPost.mockResolvedValue({
        headers: {
          'content-type': 'multipart/mixed; boundary=boundary_x',
        },
        data: [
          // 0: 404 not found
          '--boundary_x',
          'content-type: application/json',
          '',
          'HTTP/1.1 404 NOT FOUND',
          '',
          // 1: no status line
          '--boundary_x',
          'content-type: application/json',
          '',
          'no status line',
          // 2: success
          '--boundary_x',
          'content-type: application/json',
          '',
          'HTTP/1.1 200 OK',
          '',
          '{"d":{"results":["someResult"]}}',
          // 3: not json
          '--boundary_x',
          'content-type: application/json',
          '',
          'HTTP/1.1 200 OK',
          '',
          'not json',
          // 4: d does not exist
          '--boundary_x',
          'content-type: application/json',
          '',
          'HTTP/1.1 200 OK',
          '',
          '{"d does not exist"}',
          // 5: d.results does not exist
          '--boundary_x',
          'content-type: application/json',
          '',
          'HTTP/1.1 200 OK',
          '',
          '{"d": {"results does not exist": {}}',
          // 6: does not exist in the response
          '--boundary_x--',
          '',
        ].join('\r\n'),
      });


      // act
      const result = await executeBatch(param);

      // assert
      expect(result.length).toBe(7);

      expect(result[0].success).toBe(false);
      expect(result[0].error.statusCode).toBe(404);
      expect(result[0].error.message).toBe('NOT FOUND');

      expect(result[1].success).toBe(false);
      expect(result[1].error.statusCode).toBe(500);
      expect(result[1].error.message).toBe('bad response');

      expect(result[2].success).toBe(true);
      expect(result[2].ids).toEqual(['someResult'])

      expect(result[3].success).toBe(false);
      expect(result[3].error.statusCode).toBe(500);
      expect(result[3].error.message).toBe('bad response');

      expect(result[4].success).toBe(false);
      expect(result[4].error.statusCode).toBe(500);
      expect(result[4].error.message).toBe('bad response');

      expect(result[5].success).toBe(false);
      expect(result[5].error.statusCode).toBe(500);
      expect(result[5].error.message).toBe('bad response');

      expect(result[6].success).toBe(false);
      expect(result[6].error.statusCode).toBe(500);
      expect(result[6].error.message).toBe('bad response');
    })
  });


});