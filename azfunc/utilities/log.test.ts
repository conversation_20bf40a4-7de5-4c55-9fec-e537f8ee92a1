import { Logger } from '@azure/functions';
import { logLongArray } from './log';

describe('logLongArray', () => {
  const mockLog = jest.fn();
  const log = mockLog as unknown as <PERSON><PERSON>;
  const prefix = 'abc';

  beforeEach(() => {
    mockLog.mockClear();
  });

  describe('when ary.length is 10, length is 2', () => {
    it('should call log for five times', () => {
      const ary = Array(9).fill('a');
      const length = 2;

      logLongArray(log, prefix, length, ary);
      expect(log).toBeCalledTimes(5);
      expect(log).toHaveBeenNthCalledWith(1, 'abc(1/5): 9, ["a","a"]');
      expect(log).toHaveBeenNthCalledWith(2, 'abc(2/5): 9, ["a","a"]');
      expect(log).toHaveBeenNthCalledWith(3, 'abc(3/5): 9, ["a","a"]');
      expect(log).toHaveBeenNthCalledWith(4, 'abc(4/5): 9, ["a","a"]');
      expect(log).toHaveBeenNthCalledWith(5, 'abc(5/5): 9, ["a"]');
    });
  });

  describe('when ary.length is 0, length is 2', () => {
    it('should call log for five times', () => {
      const ary = Array(0).fill('a');
      const length = 2;

      logLongArray(log, prefix, length, ary);
      expect(log).toBeCalledTimes(1);
      expect(log).toHaveBeenNthCalledWith(1, 'abc(1/1): 0, []');
    });
  });

  describe('when ary.length is 1, length is 2', () => {
    it('should call log for five times', () => {
      const ary = Array(1).fill('a');
      const length = 2;

      logLongArray(log, prefix, length, ary);
      expect(log).toBeCalledTimes(1);
      expect(log).toHaveBeenNthCalledWith(1, 'abc(1/1): 1, ["a"]');
    });
  });
});
