import dayjs = require("dayjs");
import 'isomorphic-fetch';
import Dictionary from "../types/Dictionary";
import IBatchResult from "../types/IBatchResult";
import { IBatchResponseData } from "../types/Graph";
import { Entity } from "@microsoft/microsoft-graph-types";
import { RequestInit } from "node-fetch";
import { eq, when } from "./switch";
import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import { BatchRequestData, Client } from "@microsoft/microsoft-graph-client";
import ISearchContext from "../types/ISearchContext";

//-----------------------
// types
//-----------------------

export type IExecuteBatchParamGraphBatch = {
  /**
   * バッチ処理をおこなうか
   */
  process: 'graphBatch',
  /**
   * Graph APIクライアント
   */
  client: Client,
  /**
   * バッチリクエストエンドポイント
   */
  endPoint?: string,
  /**
   * 認証トークン
   */
  token?: string,
  /**
   * バッチ実行の各要素を生成するために getRequest に渡すプロパティ
   */
  context: ISearchContext[],
  /**
   * バッチ実行の各リクエストが更新処理かどうかを返すコールバック
   * @param prop propertiesの1要素
   * @returns 更新処理かどうか
   */
  getIsUpdating?: (prop: Dictionary<string, string>) => boolean,
  /**
   * バッチ実行の各リクエストに使用するクライアントを作成するコールバック
   * @param prop propertiesの1要素
   * @returns バッチ内の各要求
   */
  getClient?: (prop: Dictionary<string, string>) => Client,
  /**
   * バッチ実行の各リクエストを作成するコールバック
   * @param context コンテキスト
   * @returns バッチ内の各要求
   */
  getRequest: (context: ISearchContext) => BatchRequestData,
  /**
   * バッチ実行の各リクエスト結果を基にIDを取得するコールバック
   * @param body リクエストの実行結果
   * @returns IDの配列
   */
  getIds: (body: any) => string[],
  /**
   * ページングを行う際に次のページを取得する際に使用する情報を取得するコールバック
   * @param context コンテキスト
   * @param body リクエストの実行結果
   * @returns 次のページを取得する際に使用する情報
   */
  getPagingContext: (context: ISearchContext, body: any[]) => Dictionary<string, string> | undefined,
  /**
   * Table Storageのキーの一部となるユニークキーを生成する
   */
  getUniqueKey: (context: ISearchContext) => string,

}

export type IExecuteBatchParamGraph = {
  /**
   * バッチ処理をおこなうか
   */
  process: 'graph',
  /**
   * Graph APIクライアント
   */
  client?: Client,
  /**
   * バッチリクエストエンドポイント
   */
  endPoint?: string,
  /**
   * 認証トークン
   */
  token?: string,
  /**
   * バッチ実行の各要素を生成するために getRequest に渡すプロパティ
   */
  context: ISearchContext[],
  /**
   * バッチ実行の各リクエストに使用するクライアントを作成するコールバック
   * @param prop propertiesの1要素
   * @returns バッチ内の各要求
   */
  getClient: (prop: Dictionary<string, string>) => Client,
  /**
   * バッチ実行の各リクエストを作成するコールバック
   * @param context コンテキスト
   * @returns バッチ内の各要求
   */
  getRequest: (context: ISearchContext) => BatchRequestData,
  /**
   * バッチ実行の各リクエスト結果を基にIDを取得するコールバック
   * @param body リクエストの実行結果
   * @returns IDの配列
   */
  getIds: (body: any) => string[],
  /**
   * ページングを行う際に次のページを取得する際に使用する情報を取得するコールバック
   * @param context コンテキスト
   * @param body リクエストの実行結果
   * @returns 次のページを取得する際に使用する情報
   */
  getPagingContext: (context: ISearchContext, body: any[]) => Dictionary<string, string> | undefined,
  /**
   * Table Storageのキーの一部となるユニークキーを生成する
   */
  getUniqueKey: (context: ISearchContext) => string,

}

export type IExecuteBatchParamBatch = {
  /**
   * バッチ処理をおこなうか
   */
  process: 'batch',
  /**
   * Graph APIクライアント
   */
  client?: Client,
  /**
   * バッチリクエストエンドポイント
   */
  endPoint: string,
  /**
   * 認証トークン
   */
  token: string,
  /**
   * バッチ実行の各要素を生成するために getRequest に渡すプロパティ
   */
  context: ISearchContext[],
  /**
   * バッチ実行の各リクエストに使用するクライアントを作成するコールバック
   * @param prop propertiesの1要素
   * @returns バッチ内の各要求
   */
  getClient?: (prop: Dictionary<string, string>) => Client,
  /**
   * バッチ実行の各リクエストを作成するコールバック
   * @param context コンテキスト
   * @returns バッチ内の各要求
   */
  getRequest: (context: ISearchContext) => BatchRequestData,
  /**
   * バッチ実行の各リクエスト結果を基にIDを取得するコールバック
   * @param body リクエストの実行結果
   * @returns IDの配列
   */
  getIds: (body: any) => string[],
  /**
   * ページングを行う際に次のページを取得する際に使用する情報を取得するコールバック
   * @param context コンテキスト
   * @param body リクエストの実行結果
   * @returns 次のページを取得する際に使用する情報
   */
  getPagingContext: (context: ISearchContext, body: any[]) => Dictionary<string, string> | undefined,
  /**
   * Table Storageのキーの一部となるユニークキーを生成する
   */
  getUniqueKey: (context: ISearchContext) => string,

}

type IApiBatchResponseData = {
  headers: Dictionary<string, string>,
  statusCode: number,
  status: string,
  body: string,
};

export type IExecuteBatchParam = IExecuteBatchParamGraphBatch | IExecuteBatchParamGraph | IExecuteBatchParamBatch;

//-----------------------
// functions
//-----------------------

export async function executeAll(param: IExecuteBatchParam): Promise<IBatchResult[]> {
  return when(param.process as string)
    .on(eq('graphBatch'), () => executeGraphBatch(param as IExecuteBatchParamGraphBatch))
    .on(eq('batch'), () => executeBatch(param as IExecuteBatchParamBatch))
    .otherwise(() => executeSequential(param as IExecuteBatchParamGraph))
}

export async function executeSequential(param: IExecuteBatchParamGraph): Promise<IBatchResult[]> {
  const { context, getClient, getRequest, getIds, getPagingContext, getUniqueKey } = param;

  return await Promise.all(context.map(async ctx => {
    try {
      const client = getClient(ctx.properties);
      const request = getRequest(ctx);

      let r = client.api(request.url);
      const init = request as RequestInit;
      if (init.headers) {
        r = r.headers(init.headers);
      }

      const result = await when(init.method ?? '')
        .on(eq('POST'), () => r.post(init.body))
        .otherwise(() => r.get());

      return {
        success: true,
        page: ctx.page,
        properties: ctx.properties,
        ids: getIds(result),
        pagingContext: getPagingContext(ctx, result.value as Entity[]),
        uniqueKey: getUniqueKey(ctx),
      };
    } catch(e) {
      return {
        success: false,
        page: ctx.page,
        properties: ctx.properties,
        error: e,
      };
    }
  }));
}

export async function executeGraphBatch(param: IExecuteBatchParamGraphBatch): Promise<IBatchResult[]> {
  const { client, context, getRequest, getIds, getPagingContext, getUniqueKey } = param;

  const boundary = 'batch';

  const requests = context.map<BatchRequestData>((ctx, index) => ({
    ...getRequest(ctx),
    id: index.toString(),
  }));

  const wholeResponse = await client
    .api('/$batch')
    .headers({
      'content-type': `multipart/mixed; boundary=${boundary}`,
    })
    .post({ requests });

  const { responses } = wholeResponse;

  const dict = (responses as IBatchResponseData[]).filter(r => r.id).reduce<Dictionary<string, IBatchResponseData>>((prev, r) => ({
    ...prev,
    [r.id!]: r,
  }), {});

  return context.map<IBatchResult>((ctx, index) => {
    const response = dict[index.toString()];
    if (!response) {
      return {
        success: false,
        page: ctx.page,
        error: new Error('not included in the result'),
        properties: ctx.properties,
      };
    }

    const success = !!response.status && 200 <= response.status && response.status < 300;
    return {
      success,
      page: ctx.page,
      error: success ? undefined : { statusCode: response.status, message: '' },
      properties: ctx.properties,
      ids: success && response.body?.value ? getIds(response.body?.value) : undefined,
      pagingContext: success && response.body?.value ? getPagingContext(ctx, response.body?.value) : undefined,
      uniqueKey: success ? getUniqueKey(ctx) : undefined,
    };
  });
}

export async function executeBatch(param: IExecuteBatchParamBatch): Promise<IBatchResult[]> {
  const { endPoint, token, context, getRequest, getIds, getPagingContext, getUniqueKey } = param;
  if (context.length === 0) {
    return [];
  }

  const { url, body, options } = createBatchRequest(endPoint, token, context, getRequest);
  const response = await axios.post(url, body, options);

  const responseParts = splitResponse(response);

  return context.map<IBatchResult>((ctx, index) => {
    const responsePart: IApiBatchResponseData | undefined = responseParts[index];
    if (!responsePart) {
      return {
        success: false,
        page: ctx.page,
        error: { statusCode: 500, message: 'bad response'},
        properties: ctx.properties,
      };
    }

    const success = !!responsePart.statusCode && 200 <= responsePart.statusCode && responsePart.statusCode < 300;
    if (!success) {
      return {
        success: false,
        page: ctx.page,
        error: { statusCode: responsePart.statusCode, message: responsePart.status },
        properties: ctx.properties,
      };
    }

    try {
      const results = responsePart.body ? JSON.parse(responsePart.body)?.d?.results : undefined;
      if (!results) {
        return {
          success: false,
          page: ctx.page,
          error: { statusCode: 500, message: 'bad response'},
          properties: ctx.properties,
        };
      }

      return {
        success: true,
        page: ctx.page,
        properties: ctx.properties,
        ids: getIds(results),
        pagingContext: getPagingContext(ctx, results),
        uniqueKey: getUniqueKey(ctx),
      };
    } catch {
      return {
        success: false,
        page: ctx.page,
        error: { statusCode: 500, message: 'bad response'},
        properties: ctx.properties,
      };
    }
  });
}

function createBatchRequest(endPoint: string, token: string, context: ISearchContext[], getRequest: (context: ISearchContext) => BatchRequestData): { url: string; body: string; options: AxiosRequestConfig<string>; } {
  const boundary = getBoundary();

  const requests = context.map<BatchRequestData>((ctx, index) => ({
    ...getRequest(ctx),
    id: index.toString(),
  }));

  // 自前でバッチ要求を作る
  const body = requests.map(r => {
    const init = r as RequestInit;
    const headers = (init.headers ?? {}) as Dictionary<string, string>;
    const isUpdating = (init.method ?? 'GET') !== 'GET';
    const batchHeaders = {
      'content-type': 'application/http',
      'content-transfer-encoding': 'binary',
      'content-id': r.id,
    }
    const toHeaderString = (headers: Dictionary<string, string>) => headers ? Object.keys(headers).map(key => `${key}: ${headers[key]}\r\n`).join('') : '';
    const batchHeaderString = toHeaderString(batchHeaders);
    const headerString = toHeaderString(headers);
    const changeSetBoundary = getBoundary();
    const {changeSet, changeSetEnd} = isUpdating ? {
      changeSet: `content-type: multipart/mixed; boundary=changeset_${changeSetBoundary}\r\n\r\n--changeset_${changeSetBoundary}\r\n`,
      changeSetEnd: `--changeset_${changeSetBoundary}--\r\n`,
    } : {
      changeSet: '',
      changeSetEnd: ''
    };
    const part = `--batch_${boundary}\r\n` +
      `${changeSet}${batchHeaderString}\r\n` +
      `${init.method ?? 'GET'} ${r.url} HTTP/1.1\r\n` +
      `${headerString}\r\n` +
      (init.body ? init.body + '\r\n\r\n' : '') +
      changeSetEnd;
    return part;
  }).join('') + `--batch_${boundary}--\r\n`;

  const url = `${endPoint}/$batch`;
  const options = {
    headers: {
      'authorization': `Bearer ${token}`,
      'content-type': `multipart/mixed; boundary=batch_${boundary}`,
    },
  };

  return {
    url,
    body,
    options
  };
}

function splitResponse(response: AxiosResponse<any, any>): IApiBatchResponseData[] {
  const contentType =  response?.headers?.['content-type'];
  if (!contentType) {
    throw new Error('response cannot be parsed');
  }

  // multipartのboundaryを取得し、それで分割する
  // boundaryは先頭と末尾にもあるため、事前にreplaceで削除してから分割する
  const boundary = contentType.replace(/.*boundary=/, '');
  if (boundary === contentType) {
    // 置換ができなかったということは、正しいContentTypeではない
    throw new Error('response cannot be parsed');
  }
  const parts = response.data.replace(new RegExp(`^--${boundary}\r\n`), '').replace(new RegExp(`--${boundary}--\r\n`), '').split(`--${boundary}\r\n`);

  return parts.map((p: string) => {
    // レスポンスヘッダ、レスポンス本体、レスポンスボディが空行で区切られる
    const [headerPart, resultPart, body] = p.split('\r\n\r\n', 3);

    // レスポンスヘッダを連想配列に格納する
    const headers = parseHeaders(headerPart);

    // レスポンス本体から、ステータスコードとテキストを抽出する
    const {statusCode, status} = parseStatus(resultPart);

    return {
      headers,
      statusCode,
      status,
      body,
    };
  });
}

function getBoundary() {
  return Math.random().toString(36).substring(2, 9);
}

/**
 * ヘッダーの内容を連想配列に格納する
 * @param headerPart 「キー: 値」形式で、CRLFを末尾に持つ
 * @returns 連想配列
 */
function parseHeaders(headerPart: string): Dictionary<string, string> {
  return headerPart.split('\r\n').reduce((prev, line) => {
    const [key, value] = line.split(/: */, 2);
    return {
      ...prev,
      [key.toLowerCase()]: value,
    }
  }, {});
}

/**
 * レスポンス本体からステータスコードとステータステキストを抽出する
 * @param resultPart レスポンス本体
 * @returns statusCode, statusを持つ連想配列
 */
function parseStatus(resultPart: string): { statusCode: number; status: string; } {
  const matches = /^HTTP\/1\.1 (\d+) ([^\r]*)/.exec(resultPart);
  if (!matches || ((matches.length ?? 0) == 0)) {
    return {
      statusCode: 500,
      status: 'bad response',
    };
  }

  const [_, statusCode, status] = matches;
  return {
    statusCode: Number.parseInt(statusCode),
    status
  };
}

