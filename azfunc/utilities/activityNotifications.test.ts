import {
  createActivityNotificationsEntityUrl,
  decorateFeedOptionsAsCompany,
  decorateFeedOptionsWithChainId,
  decorateFeedOptionsWithEntityUrl,
} from './activityNotifications';
import { GeraniumActivity, GeraniumActivityParameters } from './const';

describe('utilities/activityNotifications', () => {

  describe('createActivityNotificationsEntityUrl', () => {
    it('should return the entityUrl', () => {
      expect(
        createActivityNotificationsEntityUrl('abc', '123'),
      ).toBe(
        'https://graph.microsoft.com/v1.0/users/abc/teamwork/installedApps/123',
      );
    });

    describe('when userId or userAppId is missing', () => {
      it('should return blank', () => {
        expect(
          createActivityNotificationsEntityUrl('', '123'),
        ).toBe(
          '',
        );

        expect(
          createActivityNotificationsEntityUrl('abc', ''),
        ).toBe(
          '',
        );
      });
    });
  });

  describe('decorateFeedOptionsAsCompany', () => {
    it('should decorate the config object', () => {
      expect(
        decorateFeedOptionsAsCompany(
          { chainId: 0 },
          'お知らせが届きました。',
        ),
      ).toStrictEqual({
        chainId: 0,
        activityType: GeraniumActivity.COMPANY,
        templateParameters: [
          {
            name: GeraniumActivityParameters[GeraniumActivity.COMPANY].name,
            value: 'お知らせが届きました。'
          },
        ],
      });
    });
  });

  describe('decorateFeedOptionsWithEntityUrl', () => {
    it('should decorate the config object', () => {
      expect(
        decorateFeedOptionsWithEntityUrl(
          { chainId: 0 },
          'abc',
          '123',
        ),
      ).toStrictEqual({
        chainId: 0,
        topic: {
          source: 'entityUrl',
          value: 'https://graph.microsoft.com/v1.0/users/abc/teamwork/installedApps/123',
        },
      });
    });
  });

  describe('decorateFeedOptionsWithChainId', () => {
    it('should decorate the config object', () => {
      expect(
        decorateFeedOptionsWithChainId(
          { activityType: 'test' },
          1234
        ),
      ).toStrictEqual({
        activityType: 'test',
        chainId: 1234,
      });
    });
  });
});
