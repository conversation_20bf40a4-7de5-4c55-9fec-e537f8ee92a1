import { Logger } from '@azure/functions';
import { splitArrayIntoChunks } from './array';

/**
 * output long array with multiple logs
 * @param log
 * @param prefix
 * @param length
 * @param ary
 */
export function logLongArray<T>(
  log: Logger, prefix: string, length: number, ary: T[],
): void {
  const lengthOrig = ary.length;

  if (lengthOrig === 0) {
    log(`${prefix}(1/1): ${lengthOrig}, ${JSON.stringify(ary)}`);
    return;
  }

  // use concat() to safe splitting without side effects
  splitArrayIntoChunks(ary.concat(), length).forEach((ids, i, groups) => {
    log(`${prefix}(${i + 1}/${groups.length}): ${lengthOrig}, ${JSON.stringify(ids)}`);
  });
}
