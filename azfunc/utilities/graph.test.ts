import { TokenCredential } from '@azure/core-auth';
import { Context } from '@azure/functions';
import {
  createGraphClient,
  createInstalledAppsUrl, createInstalledAppsUrlWithAppIdFilter,
  createTeamsAppIdFilterString,
  createTransitiveMembersUrl, fetchGroupUsers, fetchJsonBatch
} from './graph';
import { TokenCredentialAuthenticationProvider } from '@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials';
import { Client,PageCollection,BatchRequestData, PageIterator } from '@microsoft/microsoft-graph-client';
import { User } from '@microsoft/microsoft-graph-types';
import { IBatchResponses } from './models';

jest.mock('@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials');
jest.mock('@microsoft/microsoft-graph-client', () => ({
  Client: { initWithMiddleware: jest.fn() },
  PageIterator: jest.fn(), // PageIteratorをMock化
}));
// PageIteratorのMockが受け取るcallbackを格納する
let callbackMemory: ((data: User) => boolean) | undefined = undefined;
const iterateMock = jest.fn();
// Mock化したPageIteratorにMockの実装として使用する関数を定義する
(PageIterator as jest.Mock).mockImplementation((client, pageCollection, callback) => {
  callbackMemory = callback;
  return {
    iterate: iterateMock
  }
})

Object.defineProperty(global, 'performance', {
  writable: true,
});

describe('utilities/graph', () => {

  // mocks
  const initWithMiddlewareMock = Client.initWithMiddleware as jest.Mock;
  const TokenCredentialAuthenticationProviderMock = TokenCredentialAuthenticationProvider as jest.Mock;

  beforeEach(() => {
    initWithMiddlewareMock.mockClear();
    TokenCredentialAuthenticationProviderMock.mockClear();
  });

  describe('createGraphClient', () => {
    it('should return client instance', () => {
      initWithMiddlewareMock.mockReturnValueOnce('mock-client-instance');

      expect(
        createGraphClient({} as TokenCredential)
      ).toBe('mock-client-instance');

      expect(initWithMiddlewareMock).toBeCalledWith(expect.objectContaining({
        authProvider: expect.anything(),
      }));

      expect(TokenCredentialAuthenticationProviderMock).toBeCalledWith(
        {},
        expect.objectContaining({
          scopes: ['https://graph.microsoft.com/.default'],
        }),
      );
    });
  });

  describe('createTransitiveMembersUrl', () => {
    it('should return the url', () => {
      expect(createTransitiveMembersUrl('abc'))
        .toBe('/groups/abc/transitiveMembers');
    });

    describe('when the groupId is missing', () => {
      it('should return blank', () => {
        expect(createTransitiveMembersUrl('')).toBe('');
      });
    });
  });

  describe('createInstalledAppsUrl', () => {
    it('should return the url', () => {
      expect(createInstalledAppsUrl('abc'))
        .toBe('/users/abc/teamwork/installedApps');
    });

    describe('when the userId is missing', () => {
      it('should return blank', () => {
        expect(createInstalledAppsUrl('')).toBe('');
      });
    });
  });

  describe('createTeamsAppIdFilterString', () => {
    it('should return the filter string', () => {
      expect(createTeamsAppIdFilterString('abc'))
        .toBe("teamsApp/externalId eq 'abc'");
    });

    describe('when the teamsAppId is missing', () => {
      it('should return blank', () => {
        expect(createTeamsAppIdFilterString('')).toBe('');
      });
    });
  });

  describe('createInstalledAppsUrlWithAppIdFilter', () => {
    it('should return the url with filter string', () => {
      expect(createInstalledAppsUrlWithAppIdFilter('abc', '123'))
        .toBe("/users/abc/teamwork/installedApps?$filter=teamsApp/externalId eq '123'");
    });

    describe('when the userId is missing', () => {
      it('should return blank', () => {
        expect(createInstalledAppsUrlWithAppIdFilter('', '123')).toBe('');
      });
    });

    describe('when the teamsAppId is missing', () => {
      it('should return the url without filter string', () => {
        expect(createInstalledAppsUrlWithAppIdFilter('abc', ''))
          .toBe('/users/abc/teamwork/installedApps');
      });
    });
  });

  describe('fetchGroupUsers', () => {
    it('should return what client.get() resolves', async () => {
      // Mockが消えてしまうので再定義
      (PageIterator as jest.Mock).mockImplementation((client, pageCollection, callback) => {
        callbackMemory = callback;
        return {
          iterate: iterateMock
        }
      })
      const userList: User[] = [
        {
          displayName: 'あいうえお',
          id: 'abc',
          userType: 'Member',
        },
        {
          displayName: 'かきくけこ',
          id: 'def',
          userType: 'Group',
        },
        {
          displayName: 'さしすせそ',
          id: 'ghi',
          userType: 'Member',
        },
      ];


      const clientMock = {
        api: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        get: jest.fn().mockResolvedValue({
          value:  userList,
        } as PageCollection),
      };

      iterateMock.mockImplementation(() => {
        userList.forEach((user) => {
          callbackMemory?.(user);
        } )
        return Promise.resolve();
      });

      const expected: User[] = [
        {
          displayName: 'あいうえお',
          id: 'abc',
          userType: 'Member',
        },
        {
          displayName: 'さしすせそ',
          id: 'ghi',
          userType: 'Member',
        },
      ];
      
      await expect(
        fetchGroupUsers(
          clientMock as unknown as Client,
          'group-id',
        ),
      ).resolves.toStrictEqual(expected);

      expect(clientMock.api).toBeCalledWith('/groups/group-id/transitiveMembers');
      expect(clientMock.select).toBeCalledWith(['displayName', 'id',"userType"]);
    });
  });

  describe('fetchJsonBatch', () => {

    const postMock = jest.fn()

    const clientMock = {
      api: jest.fn(
        function() {
          return {
            post: jest.fn()
          };
      }),
      post: postMock,
    };

    // const client = clientMock as unknown as Client;

    const loggerMock = jest.fn();
    const logger = loggerMock as unknown as Context['log'];

    beforeEach(() => {
      clientMock.api.mockClear();
      clientMock.post.mockClear();
      loggerMock.mockClear();
    });

    describe('when the post() resolves', () => {
      beforeEach(() => {
        postMock.mockResolvedValue({
          value: { data: 'mock' },
        });
      });

      it('should return what post() resolves', async () => {
        const clientMock = {
          api: jest.fn().mockReturnThis(),
          post: postMock,
        };
        const client = clientMock as unknown as Client;

        await expect(
          fetchJsonBatch(
            logger,
            client,
            [{ id: 'abc', url: '123' }],
          ),
        ).resolves.toStrictEqual({
          value: { data: 'mock' },
        });

        expect(clientMock.post).toBeCalledWith({
          requests: [{ id: 'abc', url: '123' }],
        });
      });
    });

    describe('when the API resolves a response partially succeeded', () => {

      const batchReq: BatchRequestData[] = [
        {
          id: 'abc',
          url: 'https://example.com/abc',
        },
        {
          id: 'def',
          url: 'https://example.com/def',
        },
        {
          id: 'ghi',
          url: 'https://example.com/ghi',
        },
        {
          id: 'jkl',
          url: 'https://example.com/jkl',
        },
      ];

      const batchRes1: IBatchResponses = {
        responses: [
          {
            id: 'abc',
            status: 200,
            body: {},
          },
          {
            id: 'def',
            status: 429,
            headers: { 'Retry-After': '10', 'Content-Type': 'application/json' },
            body: {
              error: {
                code: 'TooManyRequests',
                message: 'Too many requests from Identifier:00421d45-dcb9-4f93-a14f-62a8884dc9a8+aa517d2d-3700-48bd-884b-c8dbcb621c21+aa517d2d-3700-48bd-884b-c8dbcb621c21 under category:throttle.teamsgraph.api_get_installed_apps_personal_scope.tenant_normal.app_normal.operation_read_sustained. Please try again later.',
              }
            },
          },
          {
            id: 'ghi',
            status: 503,
            // a mock result without Retry-After
            headers: { 'Content-Type': 'application/json' },
          },
          {
            id: 'jkl',
            status: 504,
            headers: { 'Retry-After': '30', 'Content-Type': 'application/json' },
          },
        ]
      };

      const batchRes2: IBatchResponses = {
        responses: [
          {
            id: 'def',
            status: 200,
            body: {},
          },
          {
            id: 'ghi',
            status: 200,
            body: {},
          },
          {
            id: 'jkl',
            status: 504,
            headers: { 'Retry-After': '20', 'Content-Type': 'application/json' },
          },
        ]
      };

      const batchRes3: IBatchResponses = {
        responses: [
          {
            id: 'jkl',
            status: 504,
            headers: { 'Retry-After': '30', 'Content-Type': 'application/json' },
          },
        ]
      };

      const expected = {
        responses: [
          {
            id: 'abc',
            status: 200,
            body: {},
          },
          {
            id: 'def',
            status: 200,
            body: {},
          },
          {
            id: 'ghi',
            status: 200,
            body: {},
          },
        ],
      };

      beforeEach((() => {
        postMock
          .mockResolvedValueOnce(batchRes1)
          .mockResolvedValueOnce(batchRes2)
          .mockResolvedValueOnce(batchRes3)
          .mockResolvedValueOnce(batchRes3);

        jest.useFakeTimers();
        const mockTimer = jest.spyOn(global, 'setTimeout');
        // just call callback for testing
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        mockTimer.mockImplementation((callback) => {
          callback?.();
        });
      }));

      it('should resolve succeeded responses', async () => {
        const clientMock = {
          api: jest.fn().mockReturnThis(),
          post: postMock,
        };
        const client = clientMock as unknown as Client;
        await expect(fetchJsonBatch(logger, client, batchReq)).resolves.toStrictEqual(expected);
      });

      it('should call setTimeout for three times', async () => {
        const clientMock = {
          api: jest.fn().mockReturnThis(),
          post: postMock,
        };
        const client = clientMock as unknown as Client;

        await fetchJsonBatch(logger, client, batchReq);

        expect(setTimeout).toHaveBeenNthCalledWith(1, expect.anything(), 30 * 1.5 * 1000);
        expect(setTimeout).toHaveBeenNthCalledWith(2, expect.anything(), 20 * 1.5 * 1000);
        expect(setTimeout).toHaveBeenNthCalledWith(3, expect.anything(), 30 * 1.5 * 1000);
        expect(setTimeout).toHaveBeenCalledTimes(3);
      });

      it('should call post() for four times', async () => {
        const clientMock = {
          api: jest.fn().mockReturnThis(),
          post: postMock,
        };
        const client = clientMock as unknown as Client;

        await fetchJsonBatch(logger, client, batchReq);

        expect(postMock).toHaveBeenNthCalledWith(1, { requests: batchReq });
        expect(postMock).toHaveBeenNthCalledWith(2, { requests: [batchReq[1], batchReq[2], batchReq[3]]});
        expect(postMock).toHaveBeenNthCalledWith(3, { requests: [batchReq[3]]});
        expect(postMock).toHaveBeenNthCalledWith(4, { requests: [batchReq[3]]});
        expect(postMock).toHaveBeenCalledTimes(4);
      });

      it('should call logger with specified messages', async () => {
        const clientMock = {
          api: jest.fn().mockReturnThis(),
          post: postMock,
        };
        const client = clientMock as unknown as Client;

        await fetchJsonBatch(logger, client, batchReq);

        expect(logger).toHaveBeenCalledWith(expect.stringMatching('FailedBatchRequests: 3, JSON: '));
        expect(logger).toHaveBeenCalledWith(expect.stringMatching('FailedBatchRequests: 1, JSON: '));

        expect(logger).toHaveBeenCalledWith(expect.stringMatching('RetryingCount: 1'));
        expect(logger).toHaveBeenCalledWith(expect.stringMatching('RetryingCount: 2'));
        expect(logger).toHaveBeenCalledWith(expect.stringMatching('RetryingCount: 3'));

        expect(logger).toHaveBeenCalledWith(expect.stringMatching('EntriesFailedToRetry: 1, JSON: '));

        expect(logger).toHaveBeenCalledTimes(7);
      });

    });
  });

});
