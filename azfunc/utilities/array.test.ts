import { splitArrayIntoChunks } from './array';

describe('utilities/array', () => {
  describe('splitArrayIntoChunks', () => {
    it('should split the original array in chunks', () => {
      expect(
        splitArrayIntoChunks([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 3)
      ).toStrictEqual([[1, 2, 3], [4, 5, 6], [7, 8, 9], [10]]);

      expect(
        splitArrayIntoChunks([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 3.25)
      ).toStrictEqual([[1, 2, 3], [4, 5, 6], [7, 8, 9], [10]]);

      expect(
        splitArrayIntoChunks([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], -2.75)
      ).toStrictEqual([[1, 2, 3], [4, 5, 6], [7, 8, 9], [10]]);
    });

    describe('when chunkSize is 0', () => {
      it('should return an array wrapping the original array', () => {
        expect(splitArrayIntoChunks(['a'], 0)).toStrictEqual([['a']]);
      });
    });
  });
});
