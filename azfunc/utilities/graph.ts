import { AuthenticationProviderOptions, Client, PageCollection, BatchRequestData, PageIteratorCallback, PageIterator } from "@microsoft/microsoft-graph-client";
import { TokenCredential } from '@azure/core-auth';
import { Context } from '@azure/functions';
import { TokenCredentialAuthenticationProvider } from '@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials';
import { User } from '@microsoft/microsoft-graph-types/microsoft-graph';
import { IBatchResponseData, IBatchResponses } from './models';
import { strToNum } from './number';
import { waitForTime } from './promise';
import { IMessageData } from '../utilities/models';

export interface ISearchQuery {
  requests: {
    entityTypes: string[],
    query: {
      queryString: string,
    },
    from?: number,
    size?: number,
  }[],
};
type GroupedResult = { noRetry: IBatchResponseData[], retry: IBatchResponseData[] };

const GRAPH_STATUS_CODES_TO_RETRY = [429, 503, 504]; // 再試行が必要なHTTPステータスコードを含む配列
const RETRY_AFTER_DEFAULT_SEC = 3; // 試行の間隔が指定されていない場合に使用される、デフォルトの再試行間隔
const MILLI_SEC = 1000; // 秒をミリ秒に変換するための定数
const BATCH_MAX_RETRY = 3; // バッチの最大リトライ回数
const RETRY_AFTER_WAIT_COEFFICIENT = 1.5; // リトライ後の待機時間を長くし、負担を軽減させるための定数

/**
 * GraphAPI用のクライアントを生成
 * @param credential
 */
export function createGraphClient(
  credential: TokenCredential,
): Client {
  return Client.initWithMiddleware({
    debugLogging: true,
    authProvider: new TokenCredentialAuthenticationProvider(
      credential,
      // TODO:scopeをenvに設定する
      { scopes: ['https://graph.microsoft.com/.default'] },
    ),
  });
}

export function createGraphClientAttane(token: string) {
  return Client.initWithMiddleware({
    debugLogging: true,
    authProvider: { getAccessToken: (authenticationProviderOptions?: AuthenticationProviderOptions) => Promise.resolve(token), },
  });
}

/**
 * groupsに所属するメンバー一覧を取得するためのURLを作成
 * @param groupId
 */
export function createTransitiveMembersUrl(groupId: string): string {
  if (!groupId) return '';
  return `/groups/${groupId}/transitiveMembers`;
}

/**
 * あるユーザーが特定のTeamsアプリをインストール済かを調べるためのURLを作成
 */
export function createInstalledAppsUrl(userId: string): string {
  if (!userId) return '';
  return `/users/${userId}/teamwork/installedApps`;
}

/**
 * teamsAppIdによるfilter文字列を生成
 * @param teamsAppId
 */
export function createTeamsAppIdFilterString(teamsAppId: string): string {
  if (!teamsAppId) return '';
  return `teamsApp/externalId eq '${teamsAppId}'`;
}

/**
 * AppIdによるフィルタ付きで、あるユーザーが特定のTeamsアプリをインストール済かを調べるためのURLを作成
 * @param userId
 * @param teamsAppId
 */
export function createInstalledAppsUrlWithAppIdFilter(userId: string, teamsAppId: string): string {
  const url = createInstalledAppsUrl(userId);
  const filter = createTeamsAppIdFilterString(teamsAppId);
  if (!url) return '';
  return `${url}${filter ? `?$filter=${filter}` : ''}`;
}

/**
 * groupsに所属するメンバー一覧を取得する
 * @param client Microsoft Graph API へのクエリを実行するためのクライアントオブジェクト
 * @param groupId 対象のグループの識別子
 */
export async function fetchGroupUsers(client: Client, groupId: string): Promise<User[]> {

  // 指定されたグループのメンバー情報を取得。
  const response: PageCollection = await client
    // groupId に基づいてメンバー情報を取得するための URL を生成
    .api(createTransitiveMembersUrl(groupId))
    // 取得するメンバー情報の属性を指定
    .select(['displayName', 'id', 'userType'])
    .get();

  // valueが存在しない場合、空の配列を返して処理を終了
  if (!response?.value) return [];

  // 後続処理で対象のグループに所属するユーザーのみを格納する
  const groupUserList: User[] = [];

  // グループとメンバーが混在しているリストからメンバーだけを抽出してgroupUserListに追加
  const callback: PageIteratorCallback = (user: User) => {
    if (user.userType === 'Member') {
      groupUserList.push(user);
    }
    return true;
  };

  // Microsoft Graphがユーザー一覧を分割して返してくるため、
  // 全ユーザー情報を取得できるようにユーザー一覧取得処理を反復実行させる
  const pageIterator = new PageIterator(client, response, callback);
  await pageIterator.iterate();

  // ユーザーデータが格納された groupUserList 配列をPromiseオブジェクトにして返す
  return Promise.resolve(groupUserList);
}

/**
 * group responses to retry or not
 * @param responses Graph API Batchリクエストのレスポンス1件分
 */
function getGroupedResult(responses: IBatchResponseData[]) {
  return responses.reduce<GroupedResult>((result, entry) => {
    const key = GRAPH_STATUS_CODES_TO_RETRY.includes(Number(entry.status)) ? 'retry' : 'noRetry';
    return {
      ...result,
      [key]: [
        ...result[key],
        entry,
      ],
    };
  }, { retry: [], noRetry: [] });
}

/**
 * post json batch request and retry for 3 times if needed
 * @param logger
 * @param client
 * @param requests
 * @param count a count of reclusive run; default value is 1; avoid next looping when the count is 3
 */
export async function fetchJsonBatch(
  logger: Context['log'], client: Client, requests: BatchRequestData[], count = 0,
): Promise<IBatchResponses> {

  if (count >= 1) {
    // log count on retrying
    logger(`RetryingCount: ${count}`);
  }

  const result = await client
    .api('/$batch')
    .post({ requests }) as IBatchResponses;

  if (!Array.isArray(result.responses)) return result;

  // group response entries by status code
  const groupedResult = getGroupedResult(result.responses);

  // exit when the retry length is 0 || the count value is equal or grater than BATCH_MAX_RETRY
  if (count >= BATCH_MAX_RETRY || groupedResult.retry.length === 0) {
    if (groupedResult.retry.length !== 0) {
      logger(`EntriesFailedToRetry: ${groupedResult.retry.length}, JSON: ${JSON.stringify(groupedResult.retry)}`);
    }
    return { responses: groupedResult.noRetry };
  }

  // log failed requests
  logger(`FailedBatchRequests: ${groupedResult.retry.length}, JSON: ${JSON.stringify(groupedResult.retry)}`);

  // prepare for the next loop

  // get a max value of Retry-After from the failed responses
  const maxRetryAfter = Math.max(RETRY_AFTER_DEFAULT_SEC, ...groupedResult.retry.map((d) => {
    return strToNum(d.headers?.['Retry-After'], 0);
  }));

  // create a dictionary from the parameter to find failed parameter
  const reqDict = requests.reduce<Record<string, BatchRequestData>>((dict, request) => {
    return { ...dict, [request.id]: request };
  }, {});

  // create a parameter for the next fetch
  const nextRequests = groupedResult.retry.map((d) => {
    return reqDict?.[d.id ?? ''];
  }).filter((r) => !!r);

  // wait some Retry-After seconds to throttle requests
  await waitForTime(maxRetryAfter * RETRY_AFTER_WAIT_COEFFICIENT * MILLI_SEC);

  // retry recursively
  const nextResult = await fetchJsonBatch(logger, client, nextRequests, count + 1);

  // return result joined with current response and retry response
  return {
    responses: [
      ...groupedResult.noRetry,
      ...Array.isArray(nextResult.responses)
        ? nextResult.responses
        : [],
    ]
  };
}

/**
 * post json batch request and retry for 3 times if needed
 * @param logger
 * @param client
 * @param requests
 * @param count a count of reclusive run; default value is 1; avoid next looping when the count is 3
 */
export async function fetchJsonBatchForMessages(
  logger: Context['log'], client: Client, requests: BatchRequestData[], count = 0,
): Promise<IBatchResponses> {

  if (count >= 1) {
    // log count on retrying
    logger(`RetryingCount: ${count}`);
  }

  const result = await client
    .api('/$batch')
    .post({ requests }) as IBatchResponses;

  if (!Array.isArray(result.responses)) return result;

  // Paginate if @odata.nextLink exists
  const paginatedResponses: IBatchResponseData[] = await Promise.all(
    result.responses.map(async (response) => {
      try {
        logger.info(`[Graph:fetchJsonBatchForMessages] Processing Batch Response User-ID: ${response.id}`);

        if (response.status === 403) {
          logger.info(`[Graph:fetchJsonBatchForMessages] Unauthorized, Skipping User-ID: ${response.id} - Status: ${response.status}`);
          return response;
        }
        if (response.status !== 200) {
          logger.info(`[Graph:fetchJsonBatchForMessages] Skipping User-ID: ${response.id} - Status: ${response.status}`);
          return response;
        }
        if (!response.body || !('@odata.nextLink' in response.body)) {
          logger.info(`[Graph:fetchJsonBatchForMessages] Skipping User-ID: ${response.id} - No @odata.nextLink in Response Body`);
          return response;
        }

        const allItems: IMessageData[] = [];
        const callback: PageIteratorCallback = (item: any) => {
          allItems.push(item);
          return true;
        };

        logger.info(`[Graph:fetchJsonBatchForMessages] Starting Pagination for User-ID: ${response.id}`);
        const pageIterator = new PageIterator(client, response.body as PageCollection, callback);
        await pageIterator.iterate();
        logger.info(`[Graph:fetchJsonBatchForMessages] Finished Pagination for User-ID: ${response.id}. Total Messages: ${allItems.length}`);

        return {
          ...response,
          body: {
            ...response.body,
            value: allItems,
            '@odata.nextLink': undefined
          }
        } as IBatchResponseData;
      } catch (error) {
        logger.error(`[Graph:fetchJsonBatchForMessages] Error Processing User-ID: ${response.id}:`, error);
        return {
          ...response,
          status: 500,
          body: {
            error: error instanceof Error ? error.message : 'Unknown Error'
          }
        } as IBatchResponseData;
      }
    })
  );
  const groupedResult = getGroupedResult(paginatedResponses);
  // logger.warn("[Graph:fetchJsonBatchForMessages] paginatedResponses:", paginatedResponses);

  // // group response entries by status code
  // logger.warn("[Graph:fetchJsonBatchForMessages] result.responses:", result.responses);
  // const groupedResult = getGroupedResult(result.responses);

  // exit when the retry length is 0 || the count value is equal or grater than BATCH_MAX_RETRY
  if (count >= BATCH_MAX_RETRY || groupedResult.retry.length === 0) {
    if (groupedResult.retry.length !== 0) {
      logger(`[Graph:fetchJsonBatchForMessages] EntriesFailedToRetry: ${groupedResult.retry.length}, JSON: ${JSON.stringify(groupedResult.retry)}`);
    }
    return { responses: groupedResult.noRetry };
  }

  // log failed requests
  logger(`[Graph:fetchJsonBatchForMessages] FailedBatchRequests: ${groupedResult.retry.length}, JSON: ${JSON.stringify(groupedResult.retry)}`);

  // prepare for the next loop

  // get a max value of Retry-After from the failed responses
  const maxRetryAfter = Math.max(RETRY_AFTER_DEFAULT_SEC, ...groupedResult.retry.map((d) => {
    return strToNum(d.headers?.['Retry-After'], 0);
  }));

  // create a dictionary from the parameter to find failed parameter
  const reqDict = requests.reduce<Record<string, BatchRequestData>>((dict, request) => {
    return { ...dict, [request.id]: request };
  }, {});

  // create a parameter for the next fetch
  const nextRequests = groupedResult.retry.map((d) => {
    return reqDict?.[d.id ?? ''];
  }).filter((r) => !!r);

  // wait some Retry-After seconds to throttle requests
  await waitForTime(maxRetryAfter * RETRY_AFTER_WAIT_COEFFICIENT * MILLI_SEC);

  // retry recursively
  const nextResult = await fetchJsonBatch(logger, client, nextRequests, count + 1);

  // return result joined with current response and retry response
  return {
    responses: [
      ...groupedResult.noRetry,
      ...Array.isArray(nextResult.responses)
        ? nextResult.responses
        : [],
    ]
  };
}