import { GeraniumActivity } from './const';
import { ITeamsActivityNotificationConfig } from './models';

/**
 * アクティビティフィードのentityUrlを生成する
 * @param userId
 * @param userAppId
 */
export function createActivityNotificationsEntityUrl(userId: string, userAppId: string): string {
  if (!userId || !userAppId) return '';
  return `https://graph.microsoft.com/v1.0/users/${userId}/teamwork/installedApps/${userAppId}`;
}


/**
 * sendActivityNotificationのURLを生成する
 * @param userId
 */
export function createSendActivityNotificationUrl(userId: string): string {
  if (!userId) return '';
  return `/users/${userId}/teamwork/sendActivityNotification`;
}


/**
 * フィード設定にアクティビティタイプとテンプレートパラメータを追加する
 * @param config
 * @param feedTitleMessage
 */
export function decorateFeedOptionsAsCompany(
  config: ITeamsActivityNotificationConfig,
  feedTitleMessage: string,
): ITeamsActivityNotificationConfig {
  return {
    ...config,
    activityType: GeraniumActivity.COMPANY,
    templateParameters: [
      // nameは個社の名前、Messageは"会社からのお知らせ"などの文言
      // 多分nameはsetting.jsonから取得している
      { name: 'name', value: feedTitleMessage },
    ],
  };
}

/**
 * フィード設定にentityUrlのパラメータを追加する
 * @param config
 * @param userId
 * @param appId
 */
export function decorateFeedOptionsWithEntityUrl(
  config: ITeamsActivityNotificationConfig,
  userId: string,
  appId: string,
): ITeamsActivityNotificationConfig {
  return {
    ...config,
    topic: {
      source: 'entityUrl',
      value: createActivityNotificationsEntityUrl(userId, appId),
    },
  };
}

export function decorateFeedOptionsWithChainId(
  config: ITeamsActivityNotificationConfig,
  chainId: number,
): ITeamsActivityNotificationConfig {
  return {
    ...config,
    chainId,
  };
}
