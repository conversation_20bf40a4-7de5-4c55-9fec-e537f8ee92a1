{
  "IsEncrypted": false,
  "Values": {
    "FUNCTIONS_WORKER_RUNTIME": "node",
    "FUNCTIONS_EXTENSION_VERSION": "~4",
    "WEBSITE_TIME_ZONE": "Tokyo Standard Time",
    "AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=stattanedevmec001;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "RetryCount": 5,
    "APPINSIGHTS_INSTRUMENTATIONKEY": "a7736d1b-4449-4690-957b-f17ef02a01b4",
    "APPLICATIONINSIGHTS_CONNECTION_STRING": "InstrumentationKey=a7736d1b-4449-4690-957b-f17ef02a01b4;IngestionEndpoint=https://japaneast-1.in.applicationinsights.azure.com/;LiveEndpoint=https://japaneast.livediagnostics.monitor.azure.com/",
    "ServiceBus": "Endpoint=sb://sb-attane-dev-001.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=2PlIX04L8G5zoP7m4TxUlhdTGPDQOI+PW+ASbCqTMFk=",
    "SearchProcessQueueName": "search-process-dev-mec-001",
    "MaxItemsPerSource": 2000,
    "TeamsAppId": "6b399905-b6c1-4abe-a15c-e694024d9907",
    "AzureClientId": "6b399905-b6c1-4abe-a15c-e694024d9907",
    "AzureClientSecret": "****************************************",
    "AzureTenantId": "52fc0f11-0c64-40d6-b802-3b80cbef8e3f",
    "GraphGroupId": "0f2a4fcf-c5d1-4d82-a104-51c32965ff2a",
    "COSMOS_DB_ENDPOINT": "https://cosmos-attane-dev-proto-001.documents.azure.com:443/",
    "COSMOS_DB_KEY": "****************************************************************************************",
    "COSMOS_DB_DATABASE": "AISearchDB",
    "COSMOS_DB_CONTAINER": "TestContainer",
    // "AES_SECRET_KEY": "jH5#nB8$pX3&kL7@mR4*tF6%wC9^vD2!qY5$hM8#jN3&aE7@",
    // "AES_IV_KEY": "e4b0f8e2a3c4d5f6a7b8c9d0e1f2a3b4",
    "AES_SECRET_KEY": "tjMcB*4sPiqpPR2R0WlQT14%$5*KdIc3AD!6gETZutfNVXPm",
    "AES_IV_KEY": "iu8qa3v5b8n45akj87m4juuvcupd7ut8",
    "MESSAGE_SEARCH_SIZE": 20,
    "MESSAGE_START_DATE": "2024-12-01",
    "MESSAGE_END_DATE": "2025-02-28",
    "MAX_GRAPH_API_USER_BATCH_COUNTS": 2,
    "MAX_MESSAGE_CHUNK_SIZE": 20,
    "GraphGroupIds": "e671e92b-c74e-4bb1-a34f-495ffa481944",
    "FeedTitleMessage": "横断検索してみませんか？AI検索機能が追加されました!",
    "AzureSubscriptionId": "d01c90c7-0e37-4c72-90e7-93c15313afe0",
    "ResourceGroupName": "rg-attane-dev-unique-mec",
    "AppServiceName": "app-attane-dev-mec-002",
    "SearchServiceName": "srch-attane-dev-proto-002",
    "SearchApiKey": "DIpUEiPc2cBU8jyV91aIp2wXdKs3t8vBqdyselzyLlAzSeBog6wX",
    "ApiVersion": "2025-05-01-preview",
    "StorageAccount": "stattanestg001",
    "IndexA": "srch-attane-index-spo-dev-proto-001",
    "IndexB": "srch-attane-index-spo-dev-proto-002",
    "SemanticConfigA": "srch-attane-semanticconf-spo-dev-proto-001",
    "SemanticConfigB": "srch-attane-semanticconf-spo-dev-proto-002",
    "IndexerA": "spo-dev-proto-001-indexer",
    "IndexerB": "spo-dev-proto-002-indexer",
    "IndexABlobName": "IndexA.json",
    "IndexBBlobName": "IndexB.json",
    "Env": "dev",
    "Company": "mec"
  }


}