{"name": "azfunc", "version": "1.0.0", "description": "", "scripts": {"build": "tsc", "watch": "tsc -w", "prestart": "npm run build", "start": "func start", "test": "jest", "test:ci": "jest --runInBand --ci --coverage --reporters=default --reporters=jest-junit --coverageReporters=cobertura --passWithNoTests"}, "jest": {"reporters": ["default", "jest-junit"], "moduleNameMapper": {"@azure/*": "<rootDir>/mocks/azure"}, "transform": {"\\.jsx?$": "babel-jest", "\\.tsx?$": "ts-jest"}, "testMatch": ["**/*.test.ts"], "globalSetup": "./jest-global-setup.js", "restoreMocks": true, "transformIgnorePatterns": ["/node_modules/(?!@microsoft/(mgt|microsoft-graph)|wc-react|lit-)"]}, "devDependencies": {"@azure/data-tables": "^13.3.0", "@azure/functions": "^3.5.1", "@microsoft/microsoft-graph-types": "2.40.0", "@types/crypto-js": "4.2.0", "@types/dotenv": "8.2.3", "@types/isomorphic-fetch": "0.0.39", "@types/jest": "29.5.14", "@types/node": "22.13.9", "@types/node-fetch": "2.6.12", "@types/qs": "6.9.18", "jest": "29.7.0", "jest-junit": "16.0.0", "ts-jest": "29.2.6", "typescript": "5.8.2"}, "dependencies": {"@azure/cosmos": "4.2.0", "@azure/identity": "4.4.1", "@azure/arm-appservice": "17.0.0", "@azure/storage-blob": "12.28.0", "@azure/search-documents": "12.1.0", "@microsoft/microsoft-graph-client": "^3.0.7", "axios": "1.8.1", "camljs": "^2.13.0", "cheerio": "1.0.0", "crypto-js": "4.2.0", "dayjs": "^1.11.13", "dotenv": "16.5.0", "isomorphic-fetch": "3.0.0", "node-fetch": "3.3.2"}, "volta": {"node": "20.18.1", "npm": "10.8.2"}}